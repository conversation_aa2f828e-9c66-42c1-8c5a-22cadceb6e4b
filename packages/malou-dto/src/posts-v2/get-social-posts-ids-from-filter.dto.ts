import z from 'zod';

import { SocialPostsListFilter } from '@malou-io/package-utils';

import { restaurantIdParamsTransformValidator } from '../common';

export const getAllPostIdsFromCurrentFilterQueryValidator = z.object({
    filter: z.nativeEnum(SocialPostsListFilter),
});
export type GetAllPostIdsFromCurrentFilterQueryDto = z.infer<typeof getAllPostIdsFromCurrentFilterQueryValidator>;

export const getAllPostIdsFromCurrentFilterParamsValidator = restaurantIdParamsTransformValidator;
export type GetAllPostIdsFromCurrentFilterParamsDto = z.infer<typeof getAllPostIdsFromCurrentFilterParamsValidator>;
