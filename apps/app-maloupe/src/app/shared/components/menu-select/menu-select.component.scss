@use '_malou_variables.scss' as *;

.menu-select {
    --mat-form-field-container-vertical-padding: 0;
    --mat-form-field-container-height: 30px;
    --mat-form-field-filled-container-color: transparent;

    ::ng-deep {
        .mdc-text-field--filled {
            --mdc-filled-text-field-active-indicator-height: 0px !important;
            padding: 0 !important;
        }

        .mat-mdc-form-field-subscript-wrapper {
            display: none;
        }

        .mat-mdc-form-field-focus-overlay {
            display: none;
        }

        .mat-mdc-select-arrow {
            & svg {
                display: none;
            }
            width: 15px;
            height: 15px;
            margin-bottom: 3px;

            &::after {
                display: block;
                content: '';
                position: absolute;
                width: 15px;
                height: 15px;
                background: url('/assets/svg-icons/chevron-down.svg') no-repeat;
            }
        }
    }

    max-width: 40px;
    margin-right: 10px;
    mat-select {
        mat-select-trigger {
            font-weight: 400;
            font-size: 16px;
            color: theme('colors.malou-text-title');
            text-transform: uppercase;
        }
    }
}

::ng-deep .menu-select-option-panel {
    border-radius: 10px !important;
    padding: 9px 0px !important;
}
