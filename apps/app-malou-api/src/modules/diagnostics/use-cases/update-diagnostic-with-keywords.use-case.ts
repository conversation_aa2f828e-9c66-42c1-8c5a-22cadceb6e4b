import { DateTime } from 'luxon';
import assert from 'node:assert/strict';
import { autoInjectable } from 'tsyringe';

import { DiagnosticDto } from '@malou-io/package-dto';
import { IDiagnostic } from '@malou-io/package-models';
import {
    AiInteractionRelatedEntityCollection,
    ApplicationLanguage,
    CcTld,
    GeoSamplePlatform,
    GMapsApiVersion,
    ILatlng,
    MalouErrorCode,
    MaloupeLocale,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { DiagnosticKeyword, GetKeywordsForDiagnosticPayload } from ':modules/diagnostics/diagnostic.interfaces';
import DiagnosticsRepository from ':modules/diagnostics/diagnostic.repository';
import { Diagnostic } from ':modules/diagnostics/entities/diagnostic.entity';
import { GetKeywordsForDiagnosticService } from ':modules/diagnostics/services/get-keywords-for-diagnostic/get-keywords-for-diagnostic.service';
import { GeoSampleWithoutIdAndDbDates } from ':modules/keywords/keywords.interface';
import { GeoSampleService } from ':modules/keywords/services/geo-sample.service';
import { GetKeywordRankingsForManyRestaurantsV3UseCase } from ':modules/keywords/use-cases/get-keyword-ranking-for-many-restaurants-v3.use-case';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { GenerateTextTranslationService } from ':services/text-translator/generate-text-translation.service';

interface FetchKeywordsRankingParams {
    keywords: DiagnosticKeyword[];
    latlng: ILatlng;
    placeId: string;
    region: CcTld;
}

const DEFAULT_NOT_FOUND_KEYWORD_RANKING = 21;

@autoInjectable()
export class UpdateDiagnosticWithKeywordsUseCase {
    constructor(
        private readonly _diagnosticsRepository: DiagnosticsRepository,
        private readonly _getKeywordsForDiagnosticService: GetKeywordsForDiagnosticService,
        private readonly _geoSampleService: GeoSampleService,
        private readonly _generateTextTranslationService: GenerateTextTranslationService,
        private readonly _getKeywordRankingsForManyRestaurantsV3UseCase: GetKeywordRankingsForManyRestaurantsV3UseCase,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    async execute(malouDiagnosticId: string): Promise<DiagnosticDto> {
        const partialDiagnostic = await this._diagnosticsRepository.getDiagnosticById(malouDiagnosticId);

        if (!partialDiagnostic) {
            throw new MalouError(MalouErrorCode.DIAGNOSTIC_NOT_FOUND, {
                message: 'Diagnostic not found',
                metadata: { malouDiagnosticId },
            });
        }

        if (partialDiagnostic.keywords && partialDiagnostic.keywords.length > 0) {
            return partialDiagnostic.toDto();
        }

        const keywordsWithRanking = await this._getRestaurantKeywords(partialDiagnostic);

        const updatedDiagnostic = await this._diagnosticsRepository.updateKeywords(partialDiagnostic.id, keywordsWithRanking);
        if (!updatedDiagnostic) {
            throw new MalouError(MalouErrorCode.DIAGNOSTIC_CANNOT_UPDATE_DIAGNOSTIC, {
                message: 'Cannot update diagnostic',
                metadata: { malouDiagnosticId },
            });
        }
        return updatedDiagnostic.toDto();
    }

    private async _getRestaurantKeywords(partialDiagnostic: Diagnostic): Promise<IDiagnostic['keywords']> {
        if (partialDiagnostic.restaurantId) {
            return await this._getMalouRestaurantKeywords(partialDiagnostic);
        } else {
            return await this._getNonMalouRestaurantKeywords(partialDiagnostic);
        }
    }

    private async _getMalouRestaurantKeywords(partialDiagnostic: Diagnostic): Promise<IDiagnostic['keywords']> {
        const now = DateTime.now();
        const restaurantsResults = await this._getKeywordRankingsForManyRestaurantsV3UseCase.execute({
            platformKey: GeoSamplePlatform.GMAPS,
            restaurantIds: [partialDiagnostic.restaurantId!],
            startDate: now.minus({ months: 1 }).toJSDate(),
            endDate: now.toJSDate(),
            doNotFetchRecentSamples: true,
        });

        assert.equal(restaurantsResults.restaurants.length, 1);
        const keywordsStats = restaurantsResults.restaurants[0]?.keywords;

        if (!keywordsStats) {
            return [];
        }

        type KeywordStats = NonNullable<IDiagnostic['keywords']>[number];

        const result = await Promise.all(
            keywordsStats.map(async (keywordStats): Promise<KeywordStats | null> => {
                const { rankHistory } = keywordStats;
                if (!rankHistory.length) {
                    return null;
                }
                const lastRanking = rankHistory[rankHistory.length - 1];
                assert(
                    Object.values(ApplicationLanguage)
                        .map((l): string => l)
                        .includes(keywordStats.languageCode)
                );
                const translatedKeyword = await this._translateKeyword(keywordStats.name, keywordStats.languageCode as ApplicationLanguage);
                return {
                    competitorsRanking: keywordStats.localCompetitorsPodium.map((competitor, index) => ({
                        placeId: competitor.placeId,
                        name: competitor.name,
                        address: competitor.address,
                        ranking: index + 1,
                    })),
                    keyword: {
                        [MaloupeLocale.FR]: translatedKeyword[MaloupeLocale.FR],
                        [MaloupeLocale.EN]: translatedKeyword[MaloupeLocale.EN],
                    },
                    restaurantRanking: lastRanking?.rank ?? DEFAULT_NOT_FOUND_KEYWORD_RANKING,
                };
            })
        );

        return result.filter((stats: KeywordStats | null): stats is KeywordStats => stats !== null);
    }

    private async _getNonMalouRestaurantKeywords(partialDiagnostic: Diagnostic): Promise<IDiagnostic['keywords']> {
        const payload = this._mapDiagnosticToPayload(partialDiagnostic);
        const region = partialDiagnostic.getRegionCcTld();

        if (!region) {
            throw new MalouError(MalouErrorCode.BAD_REQUEST, {
                message: 'Invalid region code',
                metadata: { region, malouDiagnosticId: partialDiagnostic.malouDiagnosticId },
            });
        }

        const keywords = await this._getKeywordsForDiagnosticService.execute(payload);

        if (!keywords || keywords.length === 0) {
            throw new MalouError(MalouErrorCode.DIAGNOSTIC_NO_KEYWORDS_FOUND, {
                message: 'No keywords found for this diagnostic',
                metadata: { malouDiagnosticId: partialDiagnostic.malouDiagnosticId },
            });
        }

        return await this._fetchKeywordsRanking({
            keywords,
            latlng: partialDiagnostic.restaurant.latlng,
            placeId: partialDiagnostic.placeId,
            region,
        });
    }

    private _mapDiagnosticToPayload = (diagnostic: Diagnostic): GetKeywordsForDiagnosticPayload => {
        const categoryNameBackend = diagnostic.restaurant.category.categoryName.backup;
        return {
            category: {
                [MaloupeLocale.FR]: diagnostic.restaurant.category.categoryName[MaloupeLocale.FR] ?? categoryNameBackend,
                [MaloupeLocale.EN]: diagnostic.restaurant.category.categoryName[MaloupeLocale.EN] ?? categoryNameBackend,
            },
            location: {
                locality: diagnostic.restaurant.address.locality,
                postalCode: diagnostic.restaurant.address.postalCode,
            },
            openingHours: diagnostic.restaurant.openingHours ?? {},
            services: {
                delivery: diagnostic.restaurant.services.delivery ?? false,
                outdoorSeating: diagnostic.restaurant.services.outdoorSeating ?? false,
                liveMusic: diagnostic.restaurant.services.liveMusic ?? false,
                servesBrunch: diagnostic.restaurant.services.servesBrunch ?? false,
                servesBreakfast: diagnostic.restaurant.services.servesBreakfast ?? false,
                goodForChildren: diagnostic.restaurant.services.goodForChildren ?? false,
                servesCocktails: diagnostic.restaurant.services.servesCocktails ?? false,
                servesCoffee: diagnostic.restaurant.services.servesCoffee ?? false,
            },
            types: diagnostic.restaurant.types,
        };
    };

    private async _fetchKeywordsRanking({
        keywords,
        latlng,
        placeId,
        region,
    }: FetchKeywordsRankingParams): Promise<IDiagnostic['keywords']> {
        const restaurant = await this._restaurantsRepository.findOne({
            filter: { placeId },
            projection: { gMapsApiVersion: 1 },
            options: { lean: true },
        });

        const gMapsApiVersion = restaurant?.gMapsApiVersion ?? GMapsApiVersion.V2;

        const samplesToFetch = keywords.map((keyword) => ({
            lat: latlng.lat,
            lng: latlng.lng,
            keyword: keyword.fr,
            platformKey: GeoSamplePlatform.GMAPS,
            region,
            version: gMapsApiVersion,
        }));
        const geoSamples = await this._geoSampleService.fetchKeywordsForLatsLngs(samplesToFetch);
        return this._getKeywordsForDiagnosticPayload(geoSamples, keywords, placeId);
    }

    private _getKeywordsForDiagnosticPayload(
        geoSamples: GeoSampleWithoutIdAndDbDates[],
        keywords: DiagnosticKeyword[],
        placeId: string
    ): IDiagnostic['keywords'] {
        const keywordsWithRanking: IDiagnostic['keywords'] = [];
        geoSamples.forEach((geoSample) => {
            const foundRanking = geoSample.ranking?.findIndex((ranking) => ranking.place_id === placeId);
            const competitorsRanking = geoSample.ranking?.map((ranking, index) => ({
                name: ranking.name,
                address: ranking.formatted_address || ranking.vicinity || '-',
                ranking: index + 1,
                placeId: ranking.place_id,
            }));
            const restaurantRanking = foundRanking && foundRanking > -1 ? foundRanking + 1 : DEFAULT_NOT_FOUND_KEYWORD_RANKING;
            keywordsWithRanking.push({
                keyword: {
                    [MaloupeLocale.FR]: geoSample.keyword,
                    [MaloupeLocale.EN]: keywords.find((keyword) => keyword.fr === geoSample.keyword)?.en,
                },
                restaurantRanking,
                competitorsRanking,
            });
        });
        return keywordsWithRanking;
    }

    private async _translateKeyword(
        keyword: string,
        baseLanguage: ApplicationLanguage
    ): Promise<{ [MaloupeLocale.FR]: string; [MaloupeLocale.EN]: string }> {
        const targetLanguage = baseLanguage === ApplicationLanguage.FR ? ApplicationLanguage.EN : ApplicationLanguage.FR;
        try {
            const translatedKeyword = await this._generateTextTranslationService.execute({
                tasks: [{ baseLanguage, text: [keyword] }],
                translationLanguages: [targetLanguage],

                relatedEntityCollection: AiInteractionRelatedEntityCollection.KEYWORDS,
            });
            if (translatedKeyword) {
                const frenchKeyword = translatedKeyword[0]?.[keyword]?.[ApplicationLanguage.FR];
                const englishKeyword = translatedKeyword[0]?.[keyword]?.[ApplicationLanguage.EN];
                return {
                    [MaloupeLocale.FR]: !frenchKeyword || frenchKeyword === '' ? keyword : frenchKeyword,
                    [MaloupeLocale.EN]: !englishKeyword || englishKeyword === '' ? keyword : englishKeyword,
                };
            }
        } catch (error: any) {
            logger.error('[UPDATE_DIAGNOSTICS_WITH_KEYWORDS] Error translating keyword', { stack: error.stack, keyword: keyword });
        }
        return {
            [MaloupeLocale.FR]: keyword,
            [MaloupeLocale.EN]: keyword,
        };
    }
}
