import 'reflect-metadata';

import ':env';

import { DateTime } from 'luxon';
import assert from 'node:assert';
import prompts from 'prompts';
import { autoInjectable, container } from 'tsyringe';

import { DayMonthYear } from '@malou-io/package-utils';

import CalendarEventsRepository from ':modules/calendar-events/calendar-events.repository';
import ':plugins/db';

@autoInjectable()
class AddDateFieldTask {
    constructor(private readonly _calendarEventsRepository: CalendarEventsRepository) {}

    async execute() {
        console.log('Start');
        const eventsWithNullDate = await this._calendarEventsRepository.find({
            filter: { date: null },
            options: { lean: true },
        });

        const response = await prompts({
            type: 'confirm',
            name: 'value',
            message: `About to update ${eventsWithNullDate.length} calendar events`,
            initial: false,
        });

        if (!response.value) {
            console.log('exit...');
            process.exit(0);
        }

        for (const event of eventsWithNullDate) {
            const startDate = event['startDate'] as Date;
            const oldDateTime = DateTime.fromJSDate(startDate).toObject();
            assert(oldDateTime.day, event._id.toString());
            assert(oldDateTime.month, event._id.toString());
            assert(oldDateTime.year, event._id.toString());
            const date: DayMonthYear = {
                day: oldDateTime.day,
                month: oldDateTime.month,
                year: oldDateTime.year,
            };

            await this._calendarEventsRepository.updateOne({ filter: { _id: event._id }, update: { $set: { date } } });
        }
    }
}

const task = container.resolve(AddDateFieldTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
