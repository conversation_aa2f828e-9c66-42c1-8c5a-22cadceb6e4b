import { ChangeDetectionStrategy, Component, computed, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { isNotNil } from '@malou-io/package-utils';

import { RestaurantsService } from ':core/services/restaurants.service';
import { InformationsContext } from ':modules/informations/informations.context';
import { LoaderProgressColorClass, LoaderProgressComponent } from ':shared/components/loader-progress/loader-progress.component';
import { DescriptionSize, Restaurant } from ':shared/models';

enum GaugeCriteriaLabel {
    NAME = 'NAME',
    ADDRESS = 'ADDRESS',
    CATEGORY = 'CATEGORY',
    REGULAR_HOURS = 'REGULAR_HOURS',
    PHONE = 'PHONE',
    DESCRIPTIONS = 'DESCRIPTIONS',
    SECONDARY_CATEGORIES = 'SECONDARY_CATEGORIES',
    ATTRIBUTES = 'ATTRIBUTES',
    WEBSITE = 'WEBSITE',
    LOGO = 'LOGO',
}

interface GaugeInformation {
    label: GaugeCriteriaLabel;
    priority: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10;
    completed: boolean;
    points: number;
}

@Component({
    selector: 'app-informations-gauge',
    imports: [MatButtonModule, MatIconModule, TranslateModule, LoaderProgressComponent],
    templateUrl: './informations-gauge.component.html',
    styleUrl: './informations-gauge.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InformationsGaugeComponent {
    private readonly _SECONDARY_CATEGORIES_MIN = 2;

    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _informationsContext = inject(InformationsContext);
    private readonly _translateService = inject(TranslateService);

    private readonly _restaurant = toSignal(this._restaurantsService.restaurantSelected$);

    // This score is computed according to the same rules as in ComputeRestaurantCompletionScoreService
    // If you change the rules here, please make sure to update the ones in the service and in the task responsible for the completion score computation: GetRestaurantsCompletionScore
    readonly gaugeInformations = computed((): GaugeInformation[] => {
        const informations: (GaugeInformation | undefined)[] = [];

        const restaurant = this._restaurant();
        if (!restaurant) {
            return [];
        }

        informations.push(this._createGaugeInformationForName(restaurant));
        informations.push(this._createGaugeInformationForAddress(restaurant));
        informations.push(this._createGaugeInformationForCategory(restaurant));
        informations.push(this._createGaugeInformationForRegularHours(restaurant));
        informations.push(this._createGaugeInformationForPhone(restaurant));
        informations.push(this._createGaugeInformationForDescriptions(restaurant));
        informations.push(this._createGaugeInformationForSecondaryCategories(restaurant));
        informations.push(this._createGaugeInformationForAttributes(restaurant));
        informations.push(this._createGaugeInformationForUrls(restaurant));
        informations.push(this._createGaugeInformationForLogo(restaurant));

        return informations.filter(isNotNil);
    });

    readonly scoreGauge = computed((): number => {
        const gaugeInformations = this.gaugeInformations();
        return gaugeInformations
            .filter((info) => info.completed)
            .map((info) => info.points)
            .reduce((a, b) => a + b, 0);
    });

    readonly progressBarColorClass = computed((): LoaderProgressColorClass => {
        if (this.scoreGauge() === 100) {
            return LoaderProgressColorClass.SUCCESS;
        }

        if (this.scoreGauge() >= 50) {
            return LoaderProgressColorClass.WARNING;
        }

        return LoaderProgressColorClass.ERROR;
    });

    readonly highestPriorityLabel = computed((): GaugeCriteriaLabel => {
        const gaugeInformations = this.gaugeInformations();

        return gaugeInformations.filter((info) => !info.completed).sort((a, b) => a.priority - b.priority)[0]?.label;
    });

    readonly ctaText = computed((): { title: string; subtitle?: string } => {
        const label = this.highestPriorityLabel();

        return (
            {
                [GaugeCriteriaLabel.NAME]: { title: this._translateService.instant('informations.gauge.cta.name') },
                [GaugeCriteriaLabel.ADDRESS]: { title: this._translateService.instant('informations.gauge.cta.address') },
                [GaugeCriteriaLabel.CATEGORY]: { title: this._translateService.instant('informations.gauge.cta.category') },
                [GaugeCriteriaLabel.REGULAR_HOURS]: { title: this._translateService.instant('informations.gauge.cta.regularHours') },
                [GaugeCriteriaLabel.PHONE]: { title: this._translateService.instant('informations.gauge.cta.phone') },
                [GaugeCriteriaLabel.DESCRIPTIONS]: {
                    title: this._translateService.instant('informations.gauge.cta.descriptions'),
                    subtitle: this._translateService.instant('informations.gauge.cta.descriptions_subtitle'),
                },
                [GaugeCriteriaLabel.SECONDARY_CATEGORIES]: {
                    title: this._translateService.instant('informations.gauge.cta.secondaryCategories'),
                },
                [GaugeCriteriaLabel.ATTRIBUTES]: { title: this._translateService.instant('informations.gauge.cta.attributes') },
                [GaugeCriteriaLabel.WEBSITE]: { title: this._translateService.instant('informations.gauge.cta.website') },
                [GaugeCriteriaLabel.LOGO]: { title: this._translateService.instant('informations.gauge.cta.logo') },
            }[label] ?? { title: this._translateService.instant('informations.gauge.cta.default') }
        );
    });

    openInformationsModal(): void {
        const label = this.highestPriorityLabel();

        if (label === GaugeCriteriaLabel.DESCRIPTIONS) {
            this._informationsContext.openRestaurantDescriptionsModal$.next();
        } else if (label === GaugeCriteriaLabel.REGULAR_HOURS) {
            this._informationsContext.openRestaurantHoursModal$.next();
        } else if (label === GaugeCriteriaLabel.ATTRIBUTES) {
            this._informationsContext.openRestaurantAttributesModal$.next();
        } else {
            this._informationsContext.openRestaurantInformationsModal$.next();
        }
    }

    private _createGaugeInformationForName(restaurant: Restaurant): GaugeInformation {
        return {
            label: GaugeCriteriaLabel.NAME,
            priority: 1,
            completed: !!restaurant.name,
            points: restaurant.isBrandBusiness() ? 20 : 10,
        };
    }

    private _createGaugeInformationForAddress(restaurant: Restaurant): GaugeInformation | undefined {
        if (restaurant.isBrandBusiness()) {
            return undefined;
        }

        const hasAddress =
            !!restaurant.address?.locality &&
            !!restaurant.address?.country &&
            !!restaurant.address?.postalCode &&
            !!restaurant.address?.regionCode;

        return {
            label: GaugeCriteriaLabel.ADDRESS,
            priority: 2,
            completed: hasAddress,
            points: 10,
        };
    }

    private _createGaugeInformationForCategory(restaurant: Restaurant): GaugeInformation {
        return {
            label: GaugeCriteriaLabel.CATEGORY,
            priority: 3,
            completed: isNotNil(restaurant.category),
            points: restaurant.isBrandBusiness() ? 20 : 10,
        };
    }

    private _createGaugeInformationForRegularHours(restaurant: Restaurant): GaugeInformation | undefined {
        if (restaurant.isBrandBusiness()) {
            return undefined;
        }

        return {
            label: GaugeCriteriaLabel.REGULAR_HOURS,
            priority: 4,
            completed: restaurant.regularHours.length > 0,
            points: 10,
        };
    }

    private _createGaugeInformationForPhone(restaurant: Restaurant): GaugeInformation | undefined {
        if (restaurant.isBrandBusiness()) {
            return undefined;
        }

        const hasPhone = !!restaurant?.phone?.digits && !!restaurant?.phone?.prefix;

        return {
            label: GaugeCriteriaLabel.PHONE,
            priority: 5,
            completed: hasPhone,
            points: 10,
        };
    }

    private _createGaugeInformationForDescriptions(restaurant: Restaurant): GaugeInformation {
        const SHORT_DESCRIPTION_SIZE_MIN = 60;
        const hasShortDescription = restaurant.descriptions.some(
            (desc) => desc.size === DescriptionSize.SHORT && !!desc.text && desc.text?.length > SHORT_DESCRIPTION_SIZE_MIN
        );
        const LONG_DESCRIPTION_SIZE_MIN = 400;
        const hasLongDescription = restaurant.descriptions.some(
            (desc) => desc.size === DescriptionSize.LONG && !!desc.text && desc.text?.length > LONG_DESCRIPTION_SIZE_MIN
        );

        return {
            label: GaugeCriteriaLabel.DESCRIPTIONS,
            priority: 6,
            completed: hasShortDescription && (restaurant.isBrandBusiness() || hasLongDescription),
            points: restaurant.isBrandBusiness() ? 20 : 10,
        };
    }

    private _createGaugeInformationForSecondaryCategories(restaurant: Restaurant): GaugeInformation {
        return {
            label: GaugeCriteriaLabel.SECONDARY_CATEGORIES,
            priority: 7,
            completed: restaurant.categoryList.length >= this._SECONDARY_CATEGORIES_MIN,
            points: 10,
        };
    }

    private _createGaugeInformationForAttributes(restaurant: Restaurant): GaugeInformation | undefined {
        if (restaurant.isBrandBusiness()) {
            return undefined;
        }

        const allPossibleAttributesLength = this._informationsContext.allCategoryAttributes().length;
        const restaurantAttributesLength = this._informationsContext.restaurantAttributes().length;

        // 80% of completed attributes is sufficient to validate this criteria
        const hasEnoughAttributesSet =
            allPossibleAttributesLength > 0 &&
            restaurantAttributesLength > 0 &&
            restaurantAttributesLength / allPossibleAttributesLength >= 0.8;

        return {
            label: GaugeCriteriaLabel.ATTRIBUTES,
            priority: 8,
            completed: hasEnoughAttributesSet,
            points: 10,
        };
    }

    private _createGaugeInformationForUrls(restaurant: Restaurant): GaugeInformation {
        const hasAtLeastOneLink =
            !!restaurant.website ||
            !!restaurant.reservationUrl ||
            !!restaurant.menuUrl ||
            !!restaurant.orderUrl ||
            !!restaurant.socialNetworkUrls?.some((socialNetworkUrl) => !!socialNetworkUrl.url);

        return {
            label: GaugeCriteriaLabel.WEBSITE,
            priority: 9,
            completed: hasAtLeastOneLink,
            points: restaurant.isBrandBusiness() ? 20 : 10,
        };
    }

    private _createGaugeInformationForLogo(restaurant: Restaurant): GaugeInformation {
        return {
            label: GaugeCriteriaLabel.LOGO,
            priority: 10,
            completed: isNotNil(restaurant.logo),
            points: 10,
        };
    }
}
