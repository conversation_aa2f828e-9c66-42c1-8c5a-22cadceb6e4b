import { TestBed } from '@angular/core/testing';
import { TranslateTestingModule } from 'ngx-translate-testing';

import { TimeInMilliseconds } from '@malou-io/package-utils';

import { FormatMillisecondsDurationPipe } from './format-millisecond-duration.pipe';
import { PluralTranslatePipe } from './plural-translate.pipe';

describe('FormatMillisecondsDurationPipe', () => {
    let pluralTranslatePipe: any;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [TranslateTestingModule.withTranslations({ fr: require('assets/i18n/fr.json') })],
            providers: [PluralTranslatePipe],
        }).compileComponents();

        pluralTranslatePipe = TestBed.inject(PluralTranslatePipe);
    });

    [null, undefined, NaN].forEach((value) => {
        it('should return "-" when milliseconds is not a number', () => {
            const expected = '-';

            const pipe = new FormatMillisecondsDurationPipe(pluralTranslatePipe);

            expect(pipe.transform(value as any)).toEqual(expected);
        });
    });

    [
        {
            expected: '1 Seconde',
            milliseconds: TimeInMilliseconds.SECOND,
        },
        {
            expected: '1 Seconde',
            milliseconds: TimeInMilliseconds.SECOND + 10,
        },
        {
            expected: '36 Secondes',
            milliseconds: 36 * TimeInMilliseconds.SECOND,
        },
        {
            expected: '1 Minute',
            milliseconds: TimeInMilliseconds.MINUTE,
        },
        {
            expected: '3 Minutes',
            milliseconds: 3 * TimeInMilliseconds.MINUTE,
        },
        {
            expected: '1 Heure',
            milliseconds: TimeInMilliseconds.HOUR,
        },
        {
            expected: '11 Heures',
            milliseconds: 11 * TimeInMilliseconds.HOUR,
        },
    ].forEach(({ expected, milliseconds }) => {
        it(`should return ${expected} when milliseconds is ${milliseconds}`, () => {
            const pipe = new FormatMillisecondsDurationPipe(pluralTranslatePipe);

            expect(pipe.transform(milliseconds)).toEqual(expected);
        });
    });

    it('should return 0 secondes as default value when milliseconds is 0', () => {
        const expected = '0 Seconde';
        const milliseconds = 0;

        const pipe = new FormatMillisecondsDurationPipe(pluralTranslatePipe);

        expect(pipe.transform(milliseconds)).toEqual(expected);
    });
});
