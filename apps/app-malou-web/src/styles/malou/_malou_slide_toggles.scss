@use '_malou_variables' as *;
@use '_malou_functions.scss' as *;

$slide-toggle-disabled-opacity: 0.5;

.mat-mdc-slide-toggle {
    --mat-slide-toggle-disabled-selected-icon-opacity: #{$slide-toggle-disabled-opacity};
    --mat-slide-toggle-disabled-unselected-icon-opacity: #{$slide-toggle-disabled-opacity};

    .mdc-switch--disabled .mdc-switch__handle-track {
        cursor: not-allowed;
    }

    .mdc-switch__handle {
        $handle-size: 16px;
        --mat-slide-toggle-with-icon-handle-size: #{$handle-size};
        --mat-slide-toggle-pressed-handle-size: #{$handle-size};
    }

    .mdc-switch__handle-track {
        $handle-track-margin: 4px;
        $handle-track-color: white;

        margin-left: $handle-track-margin;
        width: calc(100% - var(--mat-slide-toggle-handle-width) - (2 * $handle-track-margin)) !important;

        --mat-slide-toggle-handle-height: 16px;
        --mat-slide-toggle-handle-shape: calc(var(--mat-slide-toggle-handle-height));
        --mat-slide-toggle-handle-width: var(--mat-slide-toggle-handle-height);

        --mat-slide-toggle-disabled-handle-opacity: #{$slide-toggle-disabled-opacity};

        --mat-slide-toggle-handle-surface-color: var(--mdc-theme-surface, $handle-track-color);

        --mat-slide-toggle-handle-shadow-color: #{$handle-track-color};

        --mat-slide-toggle-handle-elevation-shadow: none;
        --mat-slide-toggle-disabled-handle-elevation-shadow: none;

        --mat-slide-toggle-selected-handle-color: #{$handle-track-color};
        --mat-slide-toggle-selected-focus-handle-color: #{$handle-track-color};
        --mat-slide-toggle-selected-hover-handle-color: #{$handle-track-color};
        --mat-slide-toggle-selected-pressed-handle-color: #{$handle-track-color};
        --mat-slide-toggle-disabled-selected-handle-color: #{$handle-track-color};

        --mat-slide-toggle-unselected-handle-color: #{$handle-track-color};
        --mat-slide-toggle-unselected-focus-handle-color: #{$handle-track-color};
        --mat-slide-toggle-unselected-hover-handle-color: #{$handle-track-color};
        --mat-slide-toggle-unselected-pressed-handle-color: #{$handle-track-color};
        --mat-slide-toggle-disabled-unselected-handle-color: #{$handle-track-color};
    }

    .mdc-switch__track {
        $selected-switch-track-color: theme('colors.malou-color-primary');
        $unselected-switch-track-color: theme('colors.malou-color-text-2/0.2');

        --mat-slide-toggle-track-height: 22px;
        --mat-slide-toggle-track-shape: calc(var(--mat-slide-toggle-track-height) / 2);
        --mat-slide-toggle-track-width: 38px;

        --mat-slide-toggle-disabled-track-opacity: #{$slide-toggle-disabled-opacity};

        --mat-slide-toggle-selected-track-color: #{$selected-switch-track-color};
        --mat-slide-toggle-selected-focus-track-color: #{$selected-switch-track-color};
        --mat-slide-toggle-selected-hover-track-color: #{$selected-switch-track-color};
        --mat-slide-toggle-selected-pressed-track-color: #{$selected-switch-track-color};
        --mat-slide-toggle-disabled-selected-track-color: #{$selected-switch-track-color};

        --mat-slide-toggle-unselected-track-color: #{$unselected-switch-track-color};
        --mat-slide-toggle-unselected-focus-track-color: #{$unselected-switch-track-color};
        --mat-slide-toggle-unselected-hover-track-color: #{$unselected-switch-track-color};
        --mat-slide-toggle-disabled-unselected-track-color: #{$unselected-switch-track-color};
        --mat-slide-toggle-unselected-pressed-track-color: #{$unselected-switch-track-color};
    }

    .mdc-switch__icon {
        $icon-size: 0px;
        $icon-color: white;

        --mat-slide-toggle-selected-icon-size: #{$icon-size};
        --mat-slide-toggle-unselected-icon-size: #{$icon-size};

        --mat-slide-toggle-selected-icon-color: #{$icon-color};
        --mat-slide-toggle-disabled-selected-icon-color: #{$icon-color};
        --mat-slide-toggle-disabled-unselected-icon-color: #{$icon-color};
        --mat-slide-toggle-unselected-icon-color: #{$icon-color};
    }
}
