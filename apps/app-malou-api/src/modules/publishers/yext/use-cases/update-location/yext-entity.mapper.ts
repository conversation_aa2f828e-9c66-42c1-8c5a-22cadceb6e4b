import { groupBy, isEqual, omitBy, sortBy, uniq, uniqWith } from 'lodash';
import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { IRestaurant, IRestaurantRegularHour, IRestaurantSpecialHour } from '@malou-io/package-models';
import {
    CountryCode,
    Day,
    descriptionSize,
    LanguageCodeISO_1,
    languagesByCountryCodes,
    MalouErrorCode,
    SizeInBytes,
    SocialNetworkKey,
    usRegionsWithAbbreviation,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { Media } from ':modules/media/entities/media.entity';
import { RestaurantPopulatedToPublish } from ':modules/platforms/use-cases/publish-on-connected-platforms/publish-on-connected-platforms.use-case';
import { YextRestaurant } from ':modules/publishers/yext/entities/yext-restaurant.entity';
import { googleCategoriesToYextEntityTypesMapping } from ':modules/publishers/yext/use-cases/create-location/google-categories-to-yext-entity-types-mapping';
import { Access } from ':modules/restaurants/entities/access.entity';
import { Address } from ':modules/restaurants/entities/address.entity';
import { BookmarkedPost } from ':modules/restaurants/entities/bookmarked-post.entity';
import { CurrentState } from ':modules/restaurants/entities/current-state.entity';
import { Description } from ':modules/restaurants/entities/description.entity';
import {
    YextEntity,
    YextEntityDayHours,
    YextEntityHolidayHours,
    YextEntityHours,
    YextEntityOpenInterval,
    YextEntityType,
} from ':providers/yext/yext.provider.interfaces';
import { SlackChannel, SlackService } from ':services/slack.service';

const LOGO_MAX_SIZE_IN_BYTES = 5 * SizeInBytes.MEGA_BYTES;
const LOGO_MAX_DIMENSIONS = {
    width: 10_000,
    height: 10_000,
};

export class YextEntityMapper {
    restaurant: YextRestaurant;
    constructor(restaurant: RestaurantPopulatedToPublish) {
        this.restaurant = this._toYextRestaurantEntity(restaurant);
    }

    mapRestaurantToYextEntity(acceptsReservations?: boolean, shouldUseCategoryIds?: boolean): YextEntity {
        if (!this.restaurant.address) {
            throw new MalouError(MalouErrorCode.CANNOT_MAP_RESTAURANT_TO_YEXT_ENTITY, {
                message: 'Cannot map restaurant to yext entity: no address',
            });
        }
        const countryCode = this.restaurant.address?.regionCode?.toLocaleUpperCase();
        const language = languagesByCountryCodes[countryCode] ?? LanguageCodeISO_1.FR;

        const primaryCategory = this.restaurant.category;
        const restaurantCategories = this.restaurant.categoryList ?? [];
        if (primaryCategory) {
            restaurantCategories.unshift(primaryCategory);
        }
        const logoMedia = this.restaurant.logoPopulated ?? null;
        const googleCategoryIds = uniq((restaurantCategories ?? []).map((category) => category.categoryId));
        const yextEntityType = this._getYextEntityTypeFromCategories(googleCategoryIds);
        const restaurantDescription = this._mapDescription(this.restaurant.descriptions);
        const restaurantDescriptionForApple = this._mapDescriptionForApple(this.restaurant.descriptions);
        const restaurantLatLng = this._mapLatLngToYextCoordinate(this.restaurant.latlng);
        const restaurantHours = this._mapRestaurantHoursToYextEntityHours({
            regularHours: this.restaurant.regularHours ?? [],
            specialHours: this.restaurant.specialHours ?? [],
        });
        const restaurantLogo = this._mapLogoUrl(logoMedia);
        const restaurantPhone = this._mapMainPhone(this.restaurant.phone);

        const result: YextEntity = {
            meta: {
                countryCode,
                language,
                // We should set the entityType here because a normal update with locationType won't change the entity type (apparently, despite being in the documentation, Yext blocked it because too many clients updated it without them noticing)
                entityType: yextEntityType,
            },
            address: this._mapAddress(this.restaurant.address, this.restaurant.name),
            name: this.restaurant.name,
            ...(googleCategoryIds.length > 0 &&
                !shouldUseCategoryIds && {
                    categories: {
                        googleBusinessProfile: googleCategoryIds,
                    },
                }),
            ...(googleCategoryIds.length > 0 &&
                shouldUseCategoryIds && {
                    categoryIds: googleCategoryIds,
                }),
            closed: this.restaurant.isClosedTemporarily,
            description: restaurantDescription,
            appleBusinessDescription: restaurantDescriptionForApple,
            displayCoordinate: restaurantLatLng,
            hours: restaurantHours,
            locationType: yextEntityType,
            logo: restaurantLogo,
            mainPhone: restaurantPhone,
            menuUrl: this.restaurant.menuUrl
                ? {
                      url: this.restaurant.menuUrl,
                  }
                : undefined,
            orderUrl: this.restaurant.orderUrl
                ? {
                      url: this.restaurant.orderUrl,
                  }
                : undefined,
            reservationUrl: this.restaurant.reservationUrl
                ? {
                      url: this.restaurant.reservationUrl,
                  }
                : undefined,
            websiteUrl: this.restaurant.website
                ? {
                      url: this.restaurant.website,
                  }
                : undefined,
            ...this._mapSocialNetworkUrls(this.restaurant),
            acceptsReservations: yextEntityType === YextEntityType.RESTAURANT ? acceptsReservations : undefined, // Hotel entities does not support this field
        };

        return omitBy(result, (value) => value === undefined) as YextEntity; // Only remove the undefined values, the type remains the same
    }

    private _getYextEntityTypeFromCategories(googleCategoryIds: string[]): YextEntityType {
        const yextEntityTypes = googleCategoryIds
            .map((googleCategoryId) => googleCategoriesToYextEntityTypesMapping[googleCategoryId])
            .filter(Boolean)
            .flat();
        const yextEntityTypesUniq = uniq(yextEntityTypes);
        let yextEntityType = YextEntityType.LOCATION;

        if (yextEntityTypesUniq.includes(YextEntityType.RESTAURANT)) {
            yextEntityType = YextEntityType.RESTAURANT;
        } else if (yextEntityTypesUniq.includes(YextEntityType.HOTEL)) {
            yextEntityType = YextEntityType.HOTEL;
        }
        return yextEntityType;
    }

    private _mapAddress(address: NonNullable<IRestaurant['address']>, restaurantName?: string): YextEntity['address'] {
        const isUsCountry = address.regionCode === CountryCode.UNITED_STATES;
        let postalCode = address.postalCode;
        if (!address.postalCode) {
            postalCode = '10000'; // Yext requires a postal code however we permitted restaurants not to have one, we put a default one and warn the team
            this._sendSlackMessageForMissingPostalCode(address, restaurantName).catch((error) => {
                logger.error('[YEXT_ENTITY_MAPPER] Error sending slack message', error);
            });
        }
        return {
            city: address.locality,
            line1: address.formattedAddress ?? '',
            postalCode: postalCode ?? '',
            countryCode: address.regionCode,
            region: isUsCountry ? this._getUsCountryRegion(address.administrativeArea ?? '') : address.administrativeArea,
        };
    }

    private async _sendSlackMessageForMissingPostalCode(address: NonNullable<IRestaurant['address']>, restaurantName?: string) {
        const slackService = container.resolve(SlackService);
        const context = await slackService.createContextForSlack();
        return slackService.sendMessage({
            text: `
                    Pushing a restaurant without postal code to Yext: ${address.formattedAddress}, ${restaurantName}${context}`,
            channel: SlackChannel.PLATFORM_UPDATES_ALERTS,
        });
    }

    private _getUsCountryRegion(administrativeArea: string): string {
        const usRegion = usRegionsWithAbbreviation.find((region) => region.regionName === administrativeArea);
        return usRegion?.abbreviation ?? administrativeArea;
    }

    private _mapLogoUrl(logoMedia: Media | null): YextEntity['logo'] | undefined {
        if (!logoMedia) {
            return undefined;
        }

        const hasOriginalUrl = !!logoMedia.urls.original;
        const hasCorrectSize = logoMedia.sizes.original && logoMedia.sizes.original < LOGO_MAX_SIZE_IN_BYTES;
        const hasCorrectDimensions =
            logoMedia.dimensions?.original &&
            logoMedia.dimensions?.original.width <= LOGO_MAX_DIMENSIONS.width &&
            logoMedia.dimensions?.original.height <= LOGO_MAX_DIMENSIONS.height;

        if (hasOriginalUrl && hasCorrectSize && hasCorrectDimensions) {
            return { image: { url: logoMedia.urls.original } };
        }

        const hasCorrectIgFitSize = logoMedia.sizes.igFit && logoMedia.sizes.igFit < LOGO_MAX_SIZE_IN_BYTES;
        if (logoMedia.urls.igFit && hasCorrectIgFitSize) {
            return { image: { url: logoMedia.urls.igFit } };
        }
        return undefined;
    }

    private _mapMainPhone(phone: IRestaurant['phone']): string | undefined {
        if (!phone?.digits || !phone?.prefix) {
            return undefined;
        }
        return `+${phone.prefix}${phone.digits}`;
    }

    private _mapDescription(descriptions?: Description[]): string | undefined {
        const longDescription = descriptions?.find(
            (description) => description.size === descriptionSize.LONG.key && !!description.text
        )?.text;
        const shortDescription = descriptions?.find(
            (description) => description.size === descriptionSize.SHORT.key && !!description.text
        )?.text;
        if (longDescription && longDescription.length >= 10) {
            return longDescription;
        }
        if (shortDescription && shortDescription.length >= 10) {
            return shortDescription;
        }
        return undefined;
    }

    private _mapDescriptionForApple(descriptions?: Description[]): string | undefined {
        const MAX_DESCRIPTION_LENGTH_FOR_APPLE = 500;
        const description: string | undefined = this._mapDescription(descriptions);

        if (!description) {
            return undefined;
        }

        if (description.length <= MAX_DESCRIPTION_LENGTH_FOR_APPLE) {
            return description;
        }

        const shortenedDescription = description.slice(0, MAX_DESCRIPTION_LENGTH_FOR_APPLE);
        const lastEndOfSentenceIndex = Math.max(
            shortenedDescription.lastIndexOf('. '), // We add a space to avoid cutting a word
            shortenedDescription.lastIndexOf('!'),
            shortenedDescription.lastIndexOf('?')
        );
        if (lastEndOfSentenceIndex === -1) {
            // If there is no end of sentence, we don't send the description
            return undefined;
        }
        return shortenedDescription.slice(0, lastEndOfSentenceIndex + 1);
    }

    private _mapLatLngToYextCoordinate(latlng: IRestaurant['latlng']): YextEntity['displayCoordinate'] | undefined {
        return latlng?.lat && latlng?.lng
            ? {
                  latitude: this._addDecimalToCoordinateIfNecesary(latlng.lat),
                  longitude: this._addDecimalToCoordinateIfNecesary(latlng.lng),
              }
            : undefined;
    }

    private _mapSocialNetworkUrls(restaurant: YextRestaurant):
        | {
              facebookPageUrl?: string;
              linkedInUrl?: string;
              tikTokUrl?: string;
              pinterestUrl?: string;
              youTubeChannelUrl?: string;
          }
        | undefined {
        if (!restaurant.socialNetworkUrls || restaurant.socialNetworkUrls.length === 0) {
            return undefined;
        }

        const tikTokUrl = restaurant.socialNetworkUrls.find((url) => url.key === SocialNetworkKey.TIKTOK)?.url;
        const youtubeUrl = restaurant.socialNetworkUrls.find((url) => url.key === SocialNetworkKey.YOUTUBE)?.url;
        const pinterestUrl = restaurant.socialNetworkUrls.find((url) => url.key === SocialNetworkKey.PINTEREST)?.url;
        const linkedInUrl = restaurant.socialNetworkUrls.find((url) => url.key === SocialNetworkKey.LINKEDIN)?.url;
        const facebookUrl = restaurant.socialNetworkUrls.find((url) => url.key === SocialNetworkKey.FACEBOOK)?.url;

        return {
            ...(facebookUrl && { facebookPageUrl: facebookUrl }),
            ...(linkedInUrl && { linkedInUrl }),
            ...(tikTokUrl && { tikTokUrl }),
            ...(pinterestUrl && { pinterestUrl }),
            ...(youtubeUrl && { youTubeChannelUrl: youtubeUrl }),
        };
    }

    private _mapRestaurantHoursToYextEntityHours({
        regularHours,
        specialHours,
    }: {
        regularHours: IRestaurant['regularHours'];
        specialHours: IRestaurant['specialHours'];
    }): YextEntityHours {
        const incomingSpecialHours = (specialHours ?? []).filter(
            ({ startDate: { year, month, day } }) =>
                DateTime.fromObject({ year, month: month + 1, day }).startOf('day') >= DateTime.now().startOf('day')
        );

        return {
            monday: this._mapRestaurantHoursToYextEntityHoursDay((regularHours ?? []).filter((hour) => hour.openDay === Day.MONDAY)),
            tuesday: this._mapRestaurantHoursToYextEntityHoursDay((regularHours ?? []).filter((hour) => hour.openDay === Day.TUESDAY)),
            wednesday: this._mapRestaurantHoursToYextEntityHoursDay((regularHours ?? []).filter((hour) => hour.openDay === Day.WEDNESDAY)),
            thursday: this._mapRestaurantHoursToYextEntityHoursDay((regularHours ?? []).filter((hour) => hour.openDay === Day.THURSDAY)),
            friday: this._mapRestaurantHoursToYextEntityHoursDay((regularHours ?? []).filter((hour) => hour.openDay === Day.FRIDAY)),
            saturday: this._mapRestaurantHoursToYextEntityHoursDay((regularHours ?? []).filter((hour) => hour.openDay === Day.SATURDAY)),
            sunday: this._mapRestaurantHoursToYextEntityHoursDay((regularHours ?? []).filter((hour) => hour.openDay === Day.SUNDAY)),
            holidayHours: this.mapRestaurantHoursToYextEntityHolidayHours(incomingSpecialHours),
        };
    }

    private _mapRestaurantHoursToYextEntityHoursDay(regularHours: IRestaurantRegularHour[]): YextEntityDayHours {
        if (regularHours.some((regularHour) => regularHour.isClosed)) {
            return {
                isClosed: true,
            };
        }
        return {
            isClosed: false,
            openIntervals: uniqWith(
                regularHours.map((regularHour) => ({
                    start: regularHour.openTime ?? '00:00',
                    end: this._mapCloseTime(regularHour.closeTime ?? '00:00'),
                })),
                isEqual
            ),
        };
    }

    private _mapCloseTime(closeTime: string): string {
        if (closeTime === '24:00') {
            return '23:59';
        }
        return closeTime;
    }

    private _formatSpecialHourDate(specialHour: IRestaurantSpecialHour): string {
        const year = specialHour.startDate.year;
        const month = specialHour.startDate.month + 1;
        const day = specialHour.startDate.day;
        return DateTime.local(year, month, day).toISODate();
    }

    mapRestaurantHoursToYextEntityHolidayHours(specialHours: IRestaurantSpecialHour[]): YextEntityHolidayHours[] {
        const specialHoursGroupedByDate = groupBy(specialHours, (specialHour) => this._formatSpecialHourDate(specialHour));
        return Object.entries(specialHoursGroupedByDate).map(([dateFormatted, specialHoursGroup]) => {
            if (specialHoursGroup.some((specialHour) => specialHour.isClosed)) {
                return {
                    date: dateFormatted,
                    isClosed: true,
                };
            }

            const openIntervals = uniqWith(
                (specialHoursGroup ?? []).map((specialHour) => ({
                    start: specialHour.openTime ?? '00:00',
                    end: this._mapCloseTime(specialHour.closeTime ?? '00:00'),
                })),
                isEqual
            );

            return {
                date: dateFormatted,
                isClosed: false,
                openIntervals: this.mergeOverlappingIntervals(openIntervals),
            };
        });
    }

    mergeOverlappingIntervals(intervals: YextEntityOpenInterval[]): YextEntityOpenInterval[] {
        if (intervals.length === 0) {
            return [];
        }

        // Sort intervals by start time. Format 'hh:mm' allows direct string comparison.
        const sortedIntervals = sortBy(intervals, 'start');

        const mergedIntervals: YextEntityOpenInterval[] = [];

        let currentInterval = sortedIntervals[0];

        for (let i = 1; i < sortedIntervals.length; i++) {
            const nextInterval = sortedIntervals[i];

            // Check if the intervals overlap or are adjacent (end of currentInterval >= start of nextInterval)
            if (currentInterval.end >= nextInterval.start) {
                // Merge the intervals by extending the current interval's end to the max of both ends
                currentInterval.end = currentInterval.end > nextInterval.end ? currentInterval.end : nextInterval.end;
            } else {
                // No overlap, push the current interval to the result and move to the next interval
                mergedIntervals.push(currentInterval);
                currentInterval = nextInterval;
            }
        }

        // Push the last merged interval
        mergedIntervals.push(currentInterval);

        return mergedIntervals;
    }

    private _addDecimalToCoordinateIfNecesary(coordinate: number): number {
        const getDecimalCount = coordinate.toString().split('.')[1]?.length ?? 0;
        const YEXT_MIN_DECIMAL_COUNT = 5;
        return getDecimalCount < YEXT_MIN_DECIMAL_COUNT ? coordinate + 0.00001 : coordinate;
    }

    private _toYextRestaurantEntity(restaurant: RestaurantPopulatedToPublish): YextRestaurant {
        return new YextRestaurant({
            id: restaurant._id.toString(),
            access: restaurant.access.map((access) => new Access(access)),
            active: restaurant.active,
            address: restaurant.address ? new Address(restaurant.address) : undefined,
            availableHoursTypeIds: restaurant.availableHoursTypeIds?.map((availableHoursTypeId) => availableHoursTypeId.toString()) ?? [],
            ai: restaurant.ai,
            bookmarkedPosts: restaurant.bookmarkedPosts.map((bookmarkedPost) => new BookmarkedPost(bookmarkedPost)),
            boosterPack: restaurant.boosterPack,
            bricks: restaurant.bricks.map((brick) => brick.toString()),
            bricksPostalCode: restaurant.bricksPostalCode,
            calendarEvents: restaurant.calendarEvents.map((calendarEvent) => {
                return calendarEvent.toString();
            }),
            calendarEventsCountry: restaurant.calendarEventsCountry,
            category: restaurant.category,
            categoryList: restaurant.categoryList,
            commentsLastUpdate: restaurant.commentsLastUpdate,
            cover: restaurant.cover?._id?.toString(),
            coverChanged: restaurant.coverChanged,
            currentState: restaurant.currentState ? new CurrentState(restaurant.currentState) : undefined,
            descriptions: restaurant.descriptions.map((description) => new Description(description)),
            email: restaurant.email ?? undefined,
            internalName: restaurant.internalName,
            isClaimed: restaurant.isClaimed,
            isClosedTemporarily: restaurant.isClosedTemporarily,
            latlng: restaurant.latlng,
            logo: restaurant.logo?._id?.toString(),
            logoPopulated: restaurant.logo
                ? new Media({
                      ...restaurant.logo,
                      id: restaurant.logo._id.toString(),
                      restaurantId: restaurant.logo.restaurantId?.toString(),
                      userId: restaurant.logo.userId?.toString(),
                      duplicatedFromRestaurantId: restaurant.logo.duplicatedFromRestaurantId?.toString(),
                      postIds: restaurant.logo.postIds?.map((e) => e.toString()),
                      originalMediaId: restaurant.logo.originalMediaId?.toString(),
                      folderId: restaurant.logo.folderId?.toString(),
                  })
                : undefined,
            logoChanged: restaurant.logoChanged,
            menuUrl: restaurant.menuUrl ?? undefined,
            name: restaurant.name,
            openingDate: restaurant.openingDate,
            orderUrl: restaurant.orderUrl ?? '',
            organizationId: restaurant.organizationId.toString(),
            otherHours: restaurant.otherHours,
            phone: restaurant.phone,
            placeId: restaurant.placeId,
            regularHours: restaurant.regularHours,
            relatedUrls: restaurant.relatedUrls,
            reservationUrl: restaurant.reservationUrl ?? '',
            reviewsLastUpdate: restaurant.reviewsLastUpdate,
            specialHours: restaurant.specialHours,
            socialId: restaurant.socialId,
            socialNetworkUrls: restaurant.socialNetworkUrls,
            type: restaurant.type,
            toolboxMenuUrl: restaurant.toolboxMenuUrl,
            uniqueKey: restaurant.uniqueKey,
            website: restaurant.website ?? undefined,
            createdAt: restaurant.createdAt,
            updatedAt: restaurant.updatedAt,
        });
    }
}
