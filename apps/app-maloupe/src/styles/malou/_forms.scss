.custom-malou-form-field {
    --mdc-filled-text-field-container-color: theme('backgroundColor.malou-light');
    --mdc-filled-text-field-focus-active-indicator-color: theme('backgroundColor.malou-light');

    .mat-mdc-form-field-infix {
        min-height: 48px;
        --mat-form-field-container-vertical-padding: 11px;
    }

    .mat-mdc-select-disabled {
        opacity: 0.5;
        --mat-select-disabled-trigger-text-color: theme('colors.malou-text');
    }

    &.mat-focused .mat-mdc-select-arrow {
        transform: rotate(180deg);
    }
}

.malou-filled-form-field {
    @extend .custom-malou-form-field;

    --mdc-filled-text-field-container-color: theme('colors.malou-white');
    --mat-select-trigger-text-size: 12px;
    --mat-select-trigger-text-weight: 500;
    --mat-select-enabled-trigger-text-color: theme('colors.malou-text');

    .mdc-text-field--filled.mdc-text-field--disabled {
        --mdc-filled-text-field-disabled-container-color: theme('colors.malou-white');
    }

    .mdc-text-field--filled {
        border-radius: 100px;
        padding: 0 20px;
        border: 1px solid theme('borderColor.malou-primary');
        --mdc-filled-text-field-active-indicator-height: 0;
    }

    &.mat-mdc-form-field:hover .mat-mdc-form-field-focus-overlay,
    &.mat-mdc-form-field.mat-focused .mat-mdc-form-field-focus-overlay {
        opacity: 0;
    }
}

.mat-form-field-disabled,
.mat-form-field-disabled input {
    cursor: not-allowed;
}

.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input::-webkit-input-placeholder,
.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder {
    --mdc-filled-text-field-input-text-placeholder-color: theme('colors.malou-text/0.5');
    --mat-form-field-disabled-input-text-placeholder-color: theme('colors.malou-text/0.25');

    font-style: italic;
}

.mdc-line-ripple {
    --mat-form-field-filled-active-indicator-height: 0;
    --mat-form-field-filled-focus-active-indicator-height: 0;
}
