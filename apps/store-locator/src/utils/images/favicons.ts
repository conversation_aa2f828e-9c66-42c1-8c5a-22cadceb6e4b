import { getImage } from 'astro:assets';

const favIconConfig: Record<
    string,
    { rel: string; type?: string; sizes: string }
> = {
    'favicon-16x16': {
        rel: 'icon',
        type: 'image/png',
        sizes: '16x16',
    },
    'favicon-32x32': {
        rel: 'icon',
        type: 'image/png',
        sizes: '32x32',
    },
    'android-chrome-36x36': {
        rel: 'icon',
        type: 'image/png',
        sizes: '36x36',
    },
    'android-chrome-48x48': {
        rel: 'icon',
        type: 'image/png',
        sizes: '48x48',
    },
    'android-chrome-72x72': {
        rel: 'icon',
        type: 'image/png',
        sizes: '72x72',
    },
    'android-chrome-96x96': {
        rel: 'icon',
        type: 'image/png',
        sizes: '96x96',
    },
    'android-chrome-144x144': {
        rel: 'icon',
        type: 'image/png',
        sizes: '144x144',
    },
    'android-chrome-192x192': {
        rel: 'icon',
        type: 'image/png',
        sizes: '192x192',
    },
    'apple-touch-icon-57x57': {
        rel: 'apple-touch-icon',
        sizes: '57x57',
    },
    'apple-touch-icon-60x60': {
        rel: 'apple-touch-icon',
        sizes: '60x60',
    },
    'apple-touch-icon-72x72': {
        rel: 'apple-touch-icon',
        sizes: '72x72',
    },
    'apple-touch-icon-76x76': {
        rel: 'apple-touch-icon',
        sizes: '76x76',
    },
    'apple-touch-icon-114x114': {
        rel: 'apple-touch-icon',
        sizes: '114x114',
    },
    'apple-touch-icon-120x120': {
        rel: 'apple-touch-icon',
        sizes: '120x120',
    },
    'apple-touch-icon-144x144': {
        rel: 'apple-touch-icon',
        sizes: '144x144',
    },
    'apple-touch-icon-152x152': {
        rel: 'apple-touch-icon',
        sizes: '152x152',
    },
    'apple-touch-icon-180x180': {
        rel: 'apple-touch-icon',
        sizes: '180x180',
    },
};

export interface FavIcon {
    url: string;
    sizes: string;
    rel: string;
    type?: string;
}

export async function generateFavIcons(src: string): Promise<FavIcon[]> {
    return await Promise.all(
        Object.values(favIconConfig).map(async ({ sizes, rel, type }) => {
            const [width, height] = sizes.split('x').map(Number);
            const data = await getImage({ src, width, height, format: 'png' });

            return {
                url: data.src,
                rel,
                ...(type && { type }),
                sizes,
            };
        }),
    );
}
