import Jo<PERSON> from 'joi';
import { groupBy, omit } from 'lodash';
import { ReadPreference } from 'mongodb';
import assert from 'node:assert';
import { singleton } from 'tsyringe';

import { AdminUpdateRestaurantBodyDto, GetAllRestaurantsQueryDto, GetManagersResponseDto } from '@malou-io/package-dto';
import { DbId, IPlatform, IRestaurant, toDbId } from '@malou-io/package-models';
import { BusinessCategory, CaslRole, getPlatformDefinition, MalouErrorCode, PlatformKey, Role } from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { decryptPassword, getGeolocationForRestaurant } from ':helpers/utils';
import CalendarEventsRepository from ':modules/calendar-events/calendar-events.repository';
import { StickerProps } from ':modules/nfc/stickers/entities/sticker.entity';
import { CreateStickerUseCase } from ':modules/nfc/stickers/use-cases/create-sticker/create-sticker.use-case';
import OrganizationsRepository from ':modules/organizations/organizations.repository';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PlatformsUseCases } from ':modules/platforms/platforms.use-cases';
import { FacebookMapper } from ':modules/platforms/platforms/facebook/facebook-mapper';
import { GmbMapper } from ':modules/platforms/platforms/gmb/gmb-mapper';
import ReportsRepository from ':modules/reports/reports.repository';
import { RestaurantAttributesRepository } from ':modules/restaurant-attributes/restaurant-attributes.repository';
import { RegionCodeToCalendarCountryMapper } from ':modules/restaurants/mappers/region-code-to-calendar-country.mapper';
import { RestaurantsDtoMapper } from ':modules/restaurants/mappers/restaurants.dto-mapper';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { UserRestaurantsRepository } from ':modules/user-restaurants/user-restaurants.repository';
import { UsersRepository } from ':modules/users/users.repository';
import { DisableRestaurantFromWheelOfFortuneUseCase } from ':modules/wheels-of-fortune/use-cases/disable-restaurant-from-wheel-of-fortune/disable-restaurant-from-wheel-of-fortune.use-case';

@singleton()
export default class RestaurantsUseCases {
    constructor(
        private readonly _usersRepository: UsersRepository,
        private readonly _userRestaurantsRepository: UserRestaurantsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _organizationsRepository: OrganizationsRepository,
        private readonly _restaurantsDtoMapper: RestaurantsDtoMapper,
        private readonly _calendarEventsRepository: CalendarEventsRepository,
        private readonly _restaurantAttributesRepository: RestaurantAttributesRepository,
        private readonly _reportsRepository: ReportsRepository,
        private readonly _createStickerUseCase: CreateStickerUseCase,
        private readonly _disableRestaurantFromWheelOfFortuneUseCase: DisableRestaurantFromWheelOfFortuneUseCase,
        private readonly _platformsUseCases: PlatformsUseCases,
        private readonly _regionCodeToCalendarCountryMapper: RegionCodeToCalendarCountryMapper,
        private readonly _gmbMapper: GmbMapper
    ) {}

    /**
     * Delete restaurant and all its linked resources (done in mongoose middleware)
     */
    deleteRestaurant(restaurantId: DbId) {
        return this._restaurantsRepository.deleteOne({ filter: { _id: restaurantId } });
    }

    async getPlatformAccess(restaurantId: DbId, platformKey: PlatformKey) {
        const { access } = await this._restaurantsRepository.findOneOrFail({
            filter: { _id: restaurantId },
            projection: { access: 1 },
            options: { lean: true },
        });

        if (!access) {
            return { error: true, message: MalouErrorCode.NOT_FOUND };
        }

        const platformAccess = access.find((a) => a.platformKey === platformKey && a.accessType === 'credentials');
        if (!platformAccess) {
            return { error: true, message: MalouErrorCode.NOT_FOUND };
        }

        const password = platformAccess.data?.password;
        assert(password, 'Password is required');
        return decryptPassword(password, Config.cryptoJs.secret);
    }

    addRestaurantForUser = (userId: string, restaurantId: string, role = CaslRole.OWNER) =>
        this._userRestaurantsRepository.upsertUserRestaurant(userId, restaurantId, role);

    updateUserRoleForRestaurant = (userId: string, restaurantId: string, role: CaslRole) =>
        this._userRestaurantsRepository.upsertUserRestaurant(userId, restaurantId, role);

    addRestaurantForAppAdmin = async (restaurantId: string) => {
        const appAdmin = await this._usersRepository.findOne({ filter: { email: Config.settings.adminEmail } });
        return appAdmin ? this.addRestaurantForUser(appAdmin._id.toString(), restaurantId) : null;
    };

    async removeRestaurantForUser(uid: string, restaurantId: string): Promise<void> {
        const dbUserId = toDbId(uid);
        const dbRestaurantId = toDbId(restaurantId);

        const user = await this._usersRepository.findOneOrFail({
            filter: { _id: dbUserId },
            projection: { lastVisitedRestaurantId: 1 },
            options: { lean: true },
        });
        if (user.lastVisitedRestaurantId?.toString() === restaurantId) {
            await this._usersRepository.findOneAndUpdate({ filter: { _id: dbUserId }, update: { lastVisitedRestaurantId: null } });
        }

        await Promise.all([
            this._userRestaurantsRepository.deleteOne({
                filter: {
                    userId: dbUserId,
                    restaurantId: dbRestaurantId,
                },
            }),
            this._reportsRepository.removeRestaurantForUser(dbUserId, dbRestaurantId),
        ]);
    }

    /**
     *
     * @param {Object} gmbProperties
     */
    _getRestaurantPropertiesFromGmbProperties = async (gmbProperties) => {
        const latlng = await getGeolocationForRestaurant(gmbProperties);
        const restaurantProperties = await this._gmbMapper.toMalouMapper({ ...gmbProperties, latlng });
        return restaurantProperties;
    };

    /**
     *
     * @param {Object} fbProperties
     */
    _getRestaurantPropertiesFromFacebookProperties = async (fbProperties, restaurantId: string | undefined): Promise<IPlatform> => {
        const fbMapper = new FacebookMapper();
        const restaurantProperties = await fbMapper.toMalouMapper(fbProperties, restaurantId);
        const isAddressComplete = this._checkIsRestaurantAddressComplete(restaurantProperties.address);
        if (!isAddressComplete) {
            return { ...restaurantProperties, address: null };
        }
        return restaurantProperties;
    };

    // TODO: make a better function by splitting facebook and gmb
    upsertRestaurant = async (properties, restaurantId: DbId | null) => {
        const restaurantType = this._getRestaurantType(properties);

        let platformKey: PlatformKey.FACEBOOK | PlatformKey.GMB;
        switch (restaurantType) {
            case BusinessCategory.BRAND:
                platformKey = PlatformKey.FACEBOOK;
                break;
            case BusinessCategory.LOCAL_BUSINESS:
            default:
                platformKey = PlatformKey.GMB;
                break;
        }

        const props = await this._getMappedPropertiesByPlatform(properties, restaurantId?.toString());
        if (props.type !== BusinessCategory.BRAND) {
            const restaurant = await this._restaurantsRepository.findOne({ filter: { placeId: properties.placeId } });
            if (restaurant) {
                if (props.latlng && !props.latlng.lat) {
                    delete props.latlng;
                }
            }
        }

        const boosterPackProp: { boosterPack: IRestaurant['boosterPack'] } | {} = restaurantId
            ? { boosterPack: { activated: false, activationDate: null } }
            : {};

        const calendarEventsCountry = this._regionCodeToCalendarCountryMapper.ToCalendarLanguage(props.address?.regionCode);
        const events = await this._calendarEventsRepository.find({ filter: { byDefault: true } });
        const propsWithMoreProperties = {
            ...props,
            calendarEvents: events.map((evt) => evt._id),
            active: true,
            placeId: properties.placeId,
            socialId: properties.socialId,
            organizationId: properties.organizationId,
            uniqueKey:
                props.type === BusinessCategory.BRAND ? `${platformKey}_${properties.socialId}` : `${platformKey}_${properties.placeId}`,
            calendarEventsCountry,
            ...boosterPackProp,
        };
        let filter = {};
        if (restaurantId) {
            filter = { _id: restaurantId };
        } else {
            filter =
                propsWithMoreProperties.type === BusinessCategory.BRAND
                    ? { socialId: propsWithMoreProperties.socialId, uniqueKey: propsWithMoreProperties.uniqueKey }
                    : { placeId: propsWithMoreProperties.placeId, uniqueKey: propsWithMoreProperties.uniqueKey };
        }

        const upsertedRestaurant = await this._restaurantsRepository.upsert({
            filter,
            // TODO: remove any
            update: propsWithMoreProperties as any,
            options: {
                lean: true,
            },
        });

        if (propsWithMoreProperties.type === BusinessCategory.LOCAL_BUSINESS && propsWithMoreProperties.attributeList) {
            // link attributes to created restaurant
            await Promise.all(
                propsWithMoreProperties.attributeList.map((attr) =>
                    this._restaurantAttributesRepository.upsert({
                        filter: { restaurantId: upsertedRestaurant._id, attributeId: attr.attributeId },
                        update: { attributeValue: attr.attributeValue },
                    })
                )
            );
        }

        await this._createRestaurantSticker(upsertedRestaurant._id.toString(), platformKey, {
            socialId: propsWithMoreProperties.placeId,
            socialLink: PlatformKey.FACEBOOK ? ((props as IPlatform).socialLink ?? undefined) : undefined,
        });

        return upsertedRestaurant;
    };

    upsertRestaurantFromPlatform = async (params, restaurantId: DbId) => {
        const platformKey = params.type === BusinessCategory.BRAND ? PlatformKey.FACEBOOK : PlatformKey.GMB;
        const fetchedData = await this._platformsUseCases.getLocationDataForPlatform({
            platformKey,
            credentialId: params.credentialId,
            socialId: params.socialId,
            apiEndpointV2: params.apiEndpointV2,
        });
        try {
            const restaurant = await this.upsertRestaurant(
                {
                    ...fetchedData,
                    ...params,
                    placeId: params.socialId,
                },
                restaurantId
            );
            return this._restaurantsRepository.getRestaurantByIdPopulated(restaurant?._id.toString());
        } catch (err) {
            if (String(err).match(/duplicate key/)) {
                logger.warn('[UPSERT_RESTAURANT_FROM_PLATFORM] Restaurant already exists');
                throw new Error('gmb_restaurant_already_exists');
            }
            throw err;
        }
    };

    /**
     * Check if user reached restaurants limit
     */
    userCanCreate = async ({
        userId,
        placeId,
        socialId,
        organizationId,
    }: {
        userId: string;
        placeId: string;
        socialId: string;
        organizationId: string;
    }): Promise<boolean> => {
        const user = await this._usersRepository.findOne({
            filter: { _id: toDbId(userId) },
            projection: { role: 1 },
            options: { lean: true },
        });

        if (user?.role === Role.ADMIN) return true;

        const organization = await this._organizationsRepository.getOrganizationById(organizationId);
        const limit = organization?.limit ?? 1;
        const restaurants = await this._restaurantsRepository.find({
            filter: { organizationId: toDbId(organizationId) },
            projection: {
                placeId: 1,
                socialId: 1,
            },
            options: { lean: true },
        });
        if (restaurants.length < limit) return true;
        if (restaurants.find((r) => r.placeId === placeId)) return true;
        if (restaurants.find((r) => r.socialId === socialId)) return true;
        return false;
    };

    /**
     * return all restaurants with platforms ids
     *
     */
    getRestaurantsWithPlatformsIds = async (
        filter: Record<string, any> = {},
        excludedFields: (keyof IRestaurant)[]
    ): Promise<(Partial<IRestaurant> & { platforms: Record<PlatformKey, string | null> })[]> => {
        const restaurants = await this._restaurantsRepository.find({ filter, options: { lean: true } });

        const allPlatforms = await this._platformsRepository.find({
            filter: {},
            projection: { socialId: 1, restaurantId: 1, key: 1 },
            options: { lean: true },
        });

        const platformByRestaurantId = groupBy(allPlatforms, 'restaurantId');

        const platformKeys = Object.values(PlatformKey);
        const filteredRestaurants = restaurants.map((restaurant) => {
            const platformsForRestaurant = platformByRestaurantId[restaurant._id.toString()] ?? [];
            const socialIdByPlatformKey: Record<PlatformKey, string | null> = platformKeys.reduce(
                (o, key) => Object.assign(o, { [key]: null }),
                {}
            ) as Record<PlatformKey, string | null>;
            for (const platform of platformsForRestaurant) {
                socialIdByPlatformKey[platform.key] = platform.socialId ?? null;
            }
            return {
                ...restaurant,
                platforms: socialIdByPlatformKey,
            };
        });
        const filteredRestaurantsWithoutExcludedFields = filteredRestaurants.filter(Boolean).map((restaurant) => {
            return omit(restaurant, excludedFields);
        });
        return filteredRestaurantsWithoutExcludedFields;
    };

    getRestaurantsByIds(restaurantIds: DbId[]): Promise<IRestaurant[]> {
        return this._restaurantsRepository.find({ filter: { _id: { $in: restaurantIds } }, options: { lean: true } });
    }

    getAllRestaurants = async ({ fields, active }: GetAllRestaurantsQueryDto): Promise<any[]> => {
        const filter: any = {};
        if (active !== undefined) {
            filter.active = active;
        }

        const options: any = {
            populate: [
                {
                    path: 'managers',
                    select: 'userId',
                    populate: [{ path: 'user', select: 'email name lastname' }],
                },
                {
                    path: 'organization',
                },
            ],
            lean: true,
            readPreference: ReadPreference.SECONDARY_PREFERRED,
        };

        return this._restaurantsRepository.find({
            filter,
            projection: Object.fromEntries(fields.map((sf) => [sf, 1])),
            options,
        });
    };

    /**
     *
     * @param {Object} properties
     */
    _getMappedPropertiesByPlatform = async (properties, restaurantId: string | undefined) => {
        const type = this._getRestaurantType(properties);
        switch (type) {
            case BusinessCategory.BRAND:
                const malouPropertiesFromFb = await this._getRestaurantPropertiesFromFacebookProperties(properties, restaurantId);
                return { ...malouPropertiesFromFb, type };
            case BusinessCategory.LOCAL_BUSINESS:
            default:
                const malouPropertiesFromGmb = await this._getRestaurantPropertiesFromGmbProperties(properties);
                return { ...malouPropertiesFromGmb, type };
        }
    };

    async getManagersForRestaurant(restaurantId: DbId): Promise<GetManagersResponseDto[]> {
        const restaurant = await this._restaurantsRepository.findOne({
            filter: { _id: restaurantId },
            projection: { managers: 1 },
            options: {
                populate: [
                    {
                        path: 'managers',
                        populate: [
                            {
                                path: 'user',
                                select: {
                                    _id: 1,
                                    name: 1,
                                    lastname: 1,
                                    email: 1,
                                    profilePicture: 1,
                                    role: 1,
                                },
                                populate: [
                                    {
                                        path: 'profilePicture',
                                        select: { urls: 1 },
                                    },
                                ],
                            },
                        ],
                    },
                ],
                lean: true,
            },
        });

        if (!restaurant) {
            throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, {
                metadata: {
                    restaurantId,
                },
            });
        }

        return this._restaurantsDtoMapper.toManagersDto(restaurant);
    }

    async adminUpdateRestaurant(restaurantId: string, update: AdminUpdateRestaurantBodyDto, userId: string) {
        const mappedUpdate: AdminUpdateRestaurantBodyDto & { boosterPack?: { activationDate?: Date | null } } = {
            ...update,
        };
        if (update.boosterPack) {
            mappedUpdate.boosterPack = {
                activated: update.boosterPack.activated,
                activationDate: update.boosterPack.activated ? new Date() : null,
            };

            if (!update.boosterPack.activated) {
                await this._disableRestaurantFromWheelOfFortuneUseCase.execute(restaurantId, userId);
            }
        }

        return this._restaurantsRepository.findOneAndUpdate({
            filter: { _id: toDbId(restaurantId) },
            update: mappedUpdate,
            options: {
                lean: true,
            },
        });
    }

    _getRestaurantType = (properties: { type: BusinessCategory }) => properties?.type;

    checkPlatformAccess = (platformAccess) => {
        Joi.assert(
            platformAccess,
            Joi.object().keys({
                platformKey: Joi.string().required(),
                accessType: Joi.string().required(),
                data: Joi.object({
                    login: Joi.string().required(),
                    password: Joi.string().allow(null).optional(),
                })
                    .allow(null)
                    .optional(),
                lastUpdated: Joi.date().optional(),
                lastVerified: Joi.date().allow(null).optional(),
                status: Joi.string().optional(),
                active: Joi.boolean().optional(),
            })
        );
    };

    private _checkIsRestaurantAddressComplete = (address: IPlatform['address'] | null): boolean => {
        if (!address) return false;
        return !!address.country && !!address.locality && !!address.postalCode && !!address.regionCode;
    };

    private async _createRestaurantSticker(
        restaurantId: string,
        platformKey: PlatformKey.GMB | PlatformKey.FACEBOOK,
        props: { socialId?: string; socialLink?: string }
    ): Promise<void> {
        const redirectionLink =
            platformKey === PlatformKey.GMB
                ? getPlatformDefinition(platformKey)?.externalReviewLink + (props.socialId ?? '')
                : (props.socialLink ?? '');

        const data: Omit<StickerProps, 'id'> = {
            restaurantId,
            platformKey,
            redirectionLink,
            active: true,
            starsRedirected: platformKey === PlatformKey.GMB ? [4, 5] : [],
            createdAt: new Date(),
            updatedAt: new Date(),
        };

        await this._createStickerUseCase.execute(data);
    }
}
