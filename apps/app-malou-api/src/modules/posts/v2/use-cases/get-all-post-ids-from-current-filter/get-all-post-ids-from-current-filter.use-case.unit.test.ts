import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { PostPublicationStatus, PostSource, SocialPostsListFilter } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { GetAllPostIdsFromCurrentFilterUseCase } from ':modules/posts/v2/use-cases/get-all-post-ids-from-current-filter/get-all-post-ids-from-current-filter.use-case';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';

describe('GetAllPostIdsFromCurrentFilterUseCase', () => {
    beforeEach(() => {
        container.reset();
        registerRepositories(['PostsRepository', 'RestaurantsRepository']);
    });

    describe('Empty result cases', () => {
        it('should return empty array if there are no posts', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data() {
                            return [];
                        },
                    },
                },
                expectedResult: (): string[] => {
                    return [];
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const getAllPostIdsFromCurrentFilterUseCase = container.resolve(GetAllPostIdsFromCurrentFilterUseCase);
            const result = await getAllPostIdsFromCurrentFilterUseCase.execute(restaurantId, SocialPostsListFilter.ALL);

            expect(result).toEqual(expectedResult);
        });

        it('should return empty array if restaurant has no social posts', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [getDefaultPost().source(PostSource.SEO).restaurantId(dependencies.restaurants()[0]._id).build()];
                        },
                    },
                },
                expectedResult: (): string[] => {
                    return [];
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const getAllPostIdsFromCurrentFilterUseCase = container.resolve(GetAllPostIdsFromCurrentFilterUseCase);
            const result = await getAllPostIdsFromCurrentFilterUseCase.execute(restaurantId, SocialPostsListFilter.ALL);

            expect(result).toEqual(expectedResult);
        });

        it('should return empty array if restaurant has only story posts', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .isStory(true)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): string[] => {
                    return [];
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const getAllPostIdsFromCurrentFilterUseCase = container.resolve(GetAllPostIdsFromCurrentFilterUseCase);
            const result = await getAllPostIdsFromCurrentFilterUseCase.execute(restaurantId, SocialPostsListFilter.ALL);

            expect(result).toEqual(expectedResult);
        });

        it('should return empty array if restaurant has no posts matching the filter', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): string[] => {
                    return [];
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const getAllPostIdsFromCurrentFilterUseCase = container.resolve(GetAllPostIdsFromCurrentFilterUseCase);
            const result = await getAllPostIdsFromCurrentFilterUseCase.execute(restaurantId, SocialPostsListFilter.DRAFT);

            expect(result).toEqual(expectedResult);
        });
    });

    describe('ALL filter cases', () => {
        it('should return all social post IDs when using ALL filter', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.DRAFT)
                                    .build(),
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .build(),
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.PENDING)
                                    .build(),
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.ERROR)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): string[] {
                    return dependencies.posts.map((post) => post._id.toString());
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const getAllPostIdsFromCurrentFilterUseCase = container.resolve(GetAllPostIdsFromCurrentFilterUseCase);
            const result = await getAllPostIdsFromCurrentFilterUseCase.execute(restaurantId, SocialPostsListFilter.ALL);

            expect(result).toEqual(expect.arrayContaining(expectedResult));
            expect(result).toHaveLength(expectedResult.length);
        });

        it('should exclude SEO posts and stories when using ALL filter', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.DRAFT)
                                    .isStory(false)
                                    .build(),
                                getDefaultPost()
                                    .source(PostSource.SEO)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.DRAFT)
                                    .build(),
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.DRAFT)
                                    .isStory(true)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): string[] {
                    // Only the first post should be included (social, non-story)
                    return [dependencies.posts[0]._id.toString()];
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const getAllPostIdsFromCurrentFilterUseCase = container.resolve(GetAllPostIdsFromCurrentFilterUseCase);
            const result = await getAllPostIdsFromCurrentFilterUseCase.execute(restaurantId, SocialPostsListFilter.ALL);

            expect(result).toEqual(expectedResult);
        });

        it('should only return posts from the specified restaurant when using ALL filter', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('1').build(), getDefaultRestaurant().uniqueKey('2').build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.DRAFT)
                                    .build(),
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .published(PostPublicationStatus.DRAFT)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): string[] {
                    // Only the first post should be included (from restaurant 1)
                    return [dependencies.posts[0]._id.toString()];
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const getAllPostIdsFromCurrentFilterUseCase = container.resolve(GetAllPostIdsFromCurrentFilterUseCase);
            const result = await getAllPostIdsFromCurrentFilterUseCase.execute(restaurantId, SocialPostsListFilter.ALL);

            expect(result).toEqual(expectedResult);
        });
    });

    describe('DRAFT filter cases', () => {
        it('should return only draft post IDs when using DRAFT filter', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.DRAFT)
                                    .build(),
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .build(),
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.PENDING)
                                    .build(),
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.ERROR)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): string[] {
                    // Only the first post should be included (draft)
                    return [dependencies.posts[0]._id.toString()];
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const getAllPostIdsFromCurrentFilterUseCase = container.resolve(GetAllPostIdsFromCurrentFilterUseCase);
            const result = await getAllPostIdsFromCurrentFilterUseCase.execute(restaurantId, SocialPostsListFilter.DRAFT);

            expect(result).toEqual(expectedResult);
        });

        it('should return multiple draft post IDs when using DRAFT filter', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.DRAFT)
                                    .build(),
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.DRAFT)
                                    .build(),
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): string[] {
                    // First two posts should be included (both drafts)
                    return [dependencies.posts[0]._id.toString(), dependencies.posts[1]._id.toString()];
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const getAllPostIdsFromCurrentFilterUseCase = container.resolve(GetAllPostIdsFromCurrentFilterUseCase);
            const result = await getAllPostIdsFromCurrentFilterUseCase.execute(restaurantId, SocialPostsListFilter.DRAFT);

            expect(result).toEqual(expect.arrayContaining(expectedResult));
            expect(result).toHaveLength(expectedResult.length);
        });
    });

    describe('ERROR filter cases', () => {
        it('should return only error post IDs when using ERROR filter', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.ERROR)
                                    .createdAt(DateTime.now().minus({ months: 1 }).toJSDate()) // Recent error
                                    .build(),
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.DRAFT)
                                    .build(),
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): string[] {
                    // Only the first post should be included (error)
                    return [dependencies.posts[0]._id.toString()];
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const getAllPostIdsFromCurrentFilterUseCase = container.resolve(GetAllPostIdsFromCurrentFilterUseCase);
            const result = await getAllPostIdsFromCurrentFilterUseCase.execute(restaurantId, SocialPostsListFilter.ERROR);

            expect(result).toEqual(expectedResult);
        });

        it('should exclude error posts older than 6 months when using ERROR filter', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.ERROR)
                                    .createdAt(DateTime.now().minus({ months: 1 }).toJSDate()) // Recent error
                                    .build(),
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.ERROR)
                                    .createdAt(DateTime.now().minus({ months: 7 }).toJSDate()) // Old error
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): string[] {
                    // Only the first post should be included (recent error)
                    return [dependencies.posts[0]._id.toString()];
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const getAllPostIdsFromCurrentFilterUseCase = container.resolve(GetAllPostIdsFromCurrentFilterUseCase);
            const result = await getAllPostIdsFromCurrentFilterUseCase.execute(restaurantId, SocialPostsListFilter.ERROR);

            expect(result).toEqual(expectedResult);
        });

        it('should return multiple recent error post IDs when using ERROR filter', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.ERROR)
                                    .createdAt(DateTime.now().minus({ months: 1 }).toJSDate())
                                    .build(),
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.ERROR)
                                    .createdAt(DateTime.now().minus({ months: 2 }).toJSDate())
                                    .build(),
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.DRAFT)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): string[] {
                    // First two posts should be included (both recent errors)
                    return [dependencies.posts[0]._id.toString(), dependencies.posts[1]._id.toString()];
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const getAllPostIdsFromCurrentFilterUseCase = container.resolve(GetAllPostIdsFromCurrentFilterUseCase);
            const result = await getAllPostIdsFromCurrentFilterUseCase.execute(restaurantId, SocialPostsListFilter.ERROR);

            expect(result).toEqual(expect.arrayContaining(expectedResult));
            expect(result).toHaveLength(expectedResult.length);
        });
    });

    describe('FEEDBACK filter cases', () => {
        it('should return all social post IDs when using FEEDBACK filter (same as ALL)', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.DRAFT)
                                    .build(),
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .build(),
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.ERROR)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): string[] {
                    return dependencies.posts.map((post) => post._id.toString());
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const getAllPostIdsFromCurrentFilterUseCase = container.resolve(GetAllPostIdsFromCurrentFilterUseCase);
            const result = await getAllPostIdsFromCurrentFilterUseCase.execute(restaurantId, SocialPostsListFilter.FEEDBACK);

            expect(result).toEqual(expect.arrayContaining(expectedResult));
            expect(result).toHaveLength(expectedResult.length);
        });
    });

    describe('Edge cases', () => {
        it('should handle non-existent restaurant ID', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [];
                        },
                    },
                    posts: {
                        data() {
                            return [];
                        },
                    },
                },
                expectedResult: (): string[] => {
                    return [];
                },
            });

            await testCase.build();

            const nonExistentRestaurantId = newDbId().toString();
            const expectedResult = testCase.getExpectedResult();

            const getAllPostIdsFromCurrentFilterUseCase = container.resolve(GetAllPostIdsFromCurrentFilterUseCase);
            const result = await getAllPostIdsFromCurrentFilterUseCase.execute(nonExistentRestaurantId, SocialPostsListFilter.ALL);

            expect(result).toEqual(expectedResult);
        });

        it('should handle mixed post types and sources correctly', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                // Should be included
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.DRAFT)
                                    .isStory(false)
                                    .build(),
                                // Should be excluded (SEO)
                                getDefaultPost()
                                    .source(PostSource.SEO)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.DRAFT)
                                    .build(),
                                // Should be excluded (story)
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.DRAFT)
                                    .isStory(true)
                                    .build(),
                                // Should be included
                                getDefaultPost()
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .isStory(false)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): string[] {
                    // Only posts 0 and 3 should be included
                    return [dependencies.posts[0]._id.toString(), dependencies.posts[3]._id.toString()];
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const getAllPostIdsFromCurrentFilterUseCase = container.resolve(GetAllPostIdsFromCurrentFilterUseCase);
            const result = await getAllPostIdsFromCurrentFilterUseCase.execute(restaurantId, SocialPostsListFilter.ALL);

            expect(result).toEqual(expect.arrayContaining(expectedResult));
            expect(result).toHaveLength(expectedResult.length);
        });
    });
});
