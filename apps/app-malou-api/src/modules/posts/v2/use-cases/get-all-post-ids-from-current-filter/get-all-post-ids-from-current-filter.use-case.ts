import { singleton } from 'tsyringe';

import { SocialPostsListFilter } from '@malou-io/package-utils';

import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';

@singleton()
export class GetAllPostIdsFromCurrentFilterUseCase {
    constructor(private readonly _postsRepository: PostsRepository) {}

    execute(restaurantId: string, filter: SocialPostsListFilter): Promise<string[]> {
        return this._postsRepository.getPostIdsFromFilter(restaurantId, filter);
    }
}
