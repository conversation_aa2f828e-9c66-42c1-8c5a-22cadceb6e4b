import assert from 'assert';
import { singleton } from 'tsyringe';

import { AiSocialPostDuplicationCaptionResponseDto, SocialTextDuplicationResponse } from '@malou-io/package-dto';
import { DbId, IPost, IRestaurant, toDbId } from '@malou-io/package-models';
import {
    getFeatureFlaggedPlatforms,
    getPlatformKeysWithReel,
    getSeoPlatformKeysWithPost,
    getSocialPlatformKeysWithPost,
    HeapEventName,
    MalouErrorCode,
    PlatformKey,
    PostPublicationStatus,
    PostSource,
    PostType,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { DuplicateSocialTextService } from ':modules/ai/services/duplicate-social-text.service';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import PostsRepository from ':modules/posts/posts.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { UsersRepository } from ':modules/users/users.repository';
import { HeapAnalyticsService } from ':plugins/heap-analytics';
import { isFeatureAvailableForUser } from ':services/experimentations-service/experimentation.service';

type PostRef = { id: string } | { bindingId: string };

const postRefToMongodbQuery = (ref: PostRef): { _id: DbId } | { bindingId: string } => {
    if ('id' in ref) {
        return { _id: toDbId(ref.id) };
    }
    return { bindingId: ref.bindingId };
};

/**
 * Despite its name, this class does not actually duplicate posts. The main purpose
 * of this class is to reword the text (also named “caption”) of the post the user
 * wants to duplicate in order to better suit the target platform.
 *
 * The `execute` function returns the new text in the field `postCaption`.
 */
@singleton()
export class DuplicateSocialPostWithAiUseCase {
    readonly MAX_ALLOWED_RESTAURANTS = 150;
    constructor(
        private readonly _duplicateSocialTextService: DuplicateSocialTextService,
        private readonly _postsRepository: PostsRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _eventTrackingService: HeapAnalyticsService,
        private readonly _usersRepository: UsersRepository
    ) {}

    async execute({
        restaurantIds,
        postRefToDuplicate,
        userId,
        postDestination,
    }: {
        restaurantIds: string[];
        postRefToDuplicate: PostRef;
        userId: string;
        postDestination: PostSource;
    }): Promise<AiSocialPostDuplicationCaptionResponseDto> {
        const post = await this._postsRepository.findOne({
            filter: postRefToMongodbQuery(postRefToDuplicate),
            options: {
                lean: true,
                select: { text: 1, language: 1, restaurantId: 1, platformId: 1 },
                sort: { _id: -1 },
            },
        });
        if (!post) {
            throw new MalouError(MalouErrorCode.POST_NOT_FOUND, {
                message: 'No post found for social duplication',
                metadata: { postRefToDuplicate, restaurantIds, userId },
            });
        }
        assert(post.restaurantId, 'No restaurantId found for post');

        const postRestaurant = await this._restaurantsRepository.findOne({
            filter: { _id: toDbId(post.restaurantId) },
            options: { lean: true, select: { _id: 1, name: 1, address: 1, ai: 1 } },
        });

        if (!postRestaurant) {
            throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, {
                message: 'No restaurant found for seo duplication',
                metadata: { postRefToDuplicate, restaurantIds, userId, platformId: post.platformId, restaurantId: post.restaurantId },
            });
        }
        if (post.published !== PostPublicationStatus.DRAFT) {
            const user = await this._usersRepository.findOne({
                filter: { _id: toDbId(userId) },
                projection: { email: 1 },
                options: { lean: true },
            });
            this._eventTrackingService.track({
                eventName: HeapEventName.DUPLICATE_POSTS,
                identity: userId,
                properties: {
                    duplicatedFromRestaurantId: post.restaurantId,
                    toRestaurantIds: restaurantIds,
                    platforms: post.keys,
                    userEmail: user?.email,
                },
            });
        }
        return Promise.all(
            restaurantIds.slice(0, this.MAX_ALLOWED_RESTAURANTS).map(async (restId) => {
                return postDestination === PostSource.SOCIAL
                    ? await this._duplicateToSocialPost(post, postRestaurant, restId, userId)
                    : await this._duplicateToSeoPost(post, postRestaurant, restId, userId);
            })
        );
    }

    private async _duplicateToSocialPost(
        post: IPost,
        postRestaurant: Pick<IRestaurant, '_id' | 'name' | 'address' | 'ai'>,
        restId: string,
        userId: string
    ): Promise<SocialTextDuplicationResponse> {
        const textServiceResponse = await this._duplicateSocialTextService.execute({
            post,
            postRestaurant,
            restaurantId: restId,
            userId,
            postDestination: PostSource.SOCIAL,
        });

        const socialPlatformKeys = post.postType === PostType.REEL ? getPlatformKeysWithReel() : getSocialPlatformKeysWithPost();
        const featureFlaggedPlatforms = getFeatureFlaggedPlatforms();
        const enabledSocialPlatformKeys: PlatformKey[] = [];

        for (const key of socialPlatformKeys) {
            const featureFlaggedPlatform = featureFlaggedPlatforms.find((platform) => platform.key === key);
            if (!featureFlaggedPlatform) {
                enabledSocialPlatformKeys.push(key);
                continue;
            }
            const isFeatureAvailable = featureFlaggedPlatform.featureFlagKey
                ? await isFeatureAvailableForUser({
                      featureName: featureFlaggedPlatform.featureFlagKey,
                      userId,
                  })
                : true;
            if (isFeatureAvailable) {
                enabledSocialPlatformKeys.push(key);
            }
        }

        let socialPlatforms = await this._platformsRepository.find({
            filter: {
                restaurantId: toDbId(restId),
                key: {
                    $in: enabledSocialPlatformKeys,
                },
            },
            projection: { key: 1, name: 1, credentials: 1, socialId: 1, address: 1 },
            options: { lean: true },
        });

        const mapstrPlatform = socialPlatforms.find((platform) => platform.key === PlatformKey.MAPSTR);
        if (mapstrPlatform) {
            if (!mapstrPlatform.credentials.length) {
                socialPlatforms = socialPlatforms.filter((platform) => platform.key !== PlatformKey.MAPSTR);
            }
        }

        const fbPlatform = socialPlatforms.find((platform) => platform.key === PlatformKey.FACEBOOK);
        const fbPlatformName = fbPlatform?.name ?? null;
        const fbPlatformCity = fbPlatform?.address?.locality ?? null;
        const fbPlatformId = fbPlatform?.socialId?.toString() ?? null;

        return {
            restaurantId: restId,
            postCaption: textServiceResponse.postCaption,
            hashtags: textServiceResponse.hashtags,
            fbPlatformName,
            fbPlatformCity,
            fbPlatformId,
            keys: socialPlatforms.map((platform) => platform.key),
        };
    }

    private async _duplicateToSeoPost(
        post: IPost,
        postRestaurant: Pick<IRestaurant, '_id' | 'name' | 'address' | 'ai'>,
        restId: string,
        userId: string
    ): Promise<SocialTextDuplicationResponse> {
        const textServiceResponse = await this._duplicateSocialTextService.execute({
            post,
            postRestaurant,
            restaurantId: restId,
            userId,
            postDestination: PostSource.SEO,
        });

        const seoPlatformKeys = getSeoPlatformKeysWithPost();
        const featureFlaggedPlatforms = getFeatureFlaggedPlatforms();
        const enabledSeoPlatformKeys: PlatformKey[] = [];

        for (const key of seoPlatformKeys) {
            const featureFlaggedPlatform = featureFlaggedPlatforms.find((platform) => platform.key === key);
            if (!featureFlaggedPlatform) {
                enabledSeoPlatformKeys.push(key);
                continue;
            }
            const isFeatureAvailable = featureFlaggedPlatform.featureFlagKey
                ? await isFeatureAvailableForUser({
                      featureName: featureFlaggedPlatform.featureFlagKey,
                      userId,
                  })
                : true;
            if (isFeatureAvailable) {
                enabledSeoPlatformKeys.push(key);
            }
        }

        const seoPlatforms = await this._platformsRepository.find({
            filter: {
                restaurantId: toDbId(restId),
                key: {
                    $in: enabledSeoPlatformKeys,
                },
            },
            projection: { key: 1, name: 1, credentials: 1 },
            options: { lean: true },
        });

        return {
            restaurantId: restId,
            postCaption: textServiceResponse.postCaption,
            hashtags: [],
            fbPlatformName: null,
            fbPlatformId: null,
            fbPlatformCity: null,
            keys: seoPlatforms.map((platform) => platform.key),
        };
    }
}
