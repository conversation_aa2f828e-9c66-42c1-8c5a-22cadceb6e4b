import { PlatformKey, PostType } from '@malou-io/package-utils';

import { RestaurantKeywordDto } from '../restaurant-keywords';
import { PaginationDto } from '../shared';
import { PostDto } from './post.dto';

export interface GetTop3PostsInsightsResponseDto {
    restaurantName: string;
    restaurantAddress?: string;
    platformKey: PlatformKey;
    createdAt: Date;
    postType: PostType;
    url: string;
    thumbnailUrl?: string;
    likes?: number;
    comments?: number;
    shares?: number;
    saves?: number;
    impressions?: number;
    engagementRate?: number;
}

// -------------------------------------------------

export interface SeoTextDuplicationResponse {
    restaurantId: string;
    postCaption: string;
    restaurantKeywords: RestaurantKeywordDto[];
}

export type AiSeoPostDuplicationCaptionResponseDto = SeoTextDuplicationResponse[];

// -------------------------------------------------

/**
 * Describe a new post to create during the post duplication process.
 *
 * DuplicateSocialPostWithAiUseCase returns these objects.
 */
export interface SocialTextDuplicationResponse {
    restaurantId: string;

    /** The text of the initial post, but reworded with AI for the target platform. */
    postCaption: string;

    hashtags: string[];
    keys: PlatformKey[];
    fbPlatformName: string | null;
    fbPlatformId: string | null;
    fbPlatformCity: string | null;
}

export type AiSocialPostDuplicationCaptionResponseDto = SocialTextDuplicationResponse[];

// -------------------------------------------------

export interface GetRestaurantPostsResponseDto {
    posts: PostDto[];
    pagination: PaginationDto;
    jobs: any;
}

// -------------------------------------------------
